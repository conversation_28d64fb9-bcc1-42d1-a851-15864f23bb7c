package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/Rabbit937/gin-demo/controllers"
	"github.com/Rabbit937/gin-demo/database"
	"github.com/Rabbit937/gin-demo/middleware"
	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化数据库
	database.InitDatabase()
	defer database.CloseDatabase()

	// 创建Gin路由器
	router := gin.Default()

	// 添加CORS中间件（可选）
	router.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建用户控制器
	userController := controllers.NewUserController()

	// 公开路由（不需要认证）
	public := router.Group("/api/v1")
	{
		public.GET("/ping", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "pong",
				"status":  "服务运行正常",
			})
		})
		public.POST("/register", userController.Register) // 用户注册
		public.POST("/login", userController.Login)       // 用户登录
	}

	// 需要认证的路由
	protected := router.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		protected.GET("/profile", userController.GetProfile) // 获取用户信息
		protected.POST("/logout", userController.Logout)     // 用户登出
	}

	// 优雅关闭
	go func() {
		// 监听中断信号
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		<-quit
		log.Println("正在关闭服务器...")

		// 关闭数据库连接
		database.CloseDatabase()
		os.Exit(0)
	}()

	// 启动服务器
	log.Println("服务器启动在 :8080 端口")
	log.Println("API文档:")
	log.Println("  POST /api/v1/register - 用户注册")
	log.Println("  POST /api/v1/login    - 用户登录")
	log.Println("  GET  /api/v1/profile  - 获取用户信息 (需要认证)")
	log.Println("  POST /api/v1/logout   - 用户登出 (需要认证)")

	if err := router.Run(":8080"); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}
