# Gin 用户登录注册系统

这是一个使用 Gin 框架构建的用户登录注册系统，包含JWT认证、密码加密、数据库操作等功能。

## 功能特性

- ✅ 用户注册
- ✅ 用户登录
- ✅ JWT Token 认证
- ✅ 密码加密（bcrypt）
- ✅ 数据库操作（GORM + SQLite）
- ✅ 输入验证
- ✅ 错误处理
- ✅ CORS 支持
- ✅ 优雅关闭

## 项目结构

```
gin-demo/
├── controllers/        # 控制器层
│   └── user_controller.go
├── database/          # 数据库配置
│   └── database.go
├── middleware/        # 中间件
│   └── auth.go
├── models/           # 数据模型
│   └── user.go
├── services/         # 服务层
│   └── user_service.go
├── main.go           # 主程序入口
├── go.mod            # Go模块文件
├── go.sum            # 依赖锁定文件
├── test_api.sh       # API测试脚本
└── README.md         # 项目说明
```

## 安装和运行

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 运行服务器

```bash
go run main.go
```

服务器将在 `http://localhost:8080` 启动。

### 3. 测试API

使用提供的测试脚本：

```bash
chmod +x test_api.sh
./test_api.sh
```

## API 接口

### 公开接口（无需认证）

#### 1. 健康检查
```
GET /api/v1/ping
```

#### 2. 用户注册
```
POST /api/v1/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 3. 用户登录
```
POST /api/v1/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

### 需要认证的接口

#### 4. 获取用户信息
```
GET /api/v1/profile
Authorization: Bearer <token>
```

#### 5. 用户登出
```
POST /api/v1/logout
Authorization: Bearer <token>
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "error": "详细错误描述"
}
```

## 环境变量

可以设置以下环境变量：

- `JWT_SECRET`: JWT签名密钥（默认: "your-secret-key-change-this-in-production"）

## 技术栈

- **Web框架**: Gin
- **数据库**: SQLite (通过 GORM)
- **认证**: JWT (golang-jwt/jwt/v5)
- **密码加密**: bcrypt
- **数据验证**: Gin的内置验证器

## 安全特性

1. **密码加密**: 使用 bcrypt 对密码进行哈希加密
2. **JWT认证**: 使用JWT token进行用户认证
3. **输入验证**: 对用户输入进行严格验证
4. **CORS支持**: 支持跨域请求
5. **错误处理**: 完善的错误处理机制

## 开发说明

### 添加新的API接口

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `controllers/` 中实现控制器
4. 在 `main.go` 中注册路由

### 数据库迁移

项目使用 GORM 的自动迁移功能，启动时会自动创建和更新数据库表结构。

## 许可证

MIT License
