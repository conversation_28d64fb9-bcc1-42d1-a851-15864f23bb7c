package controllers

import (
	"net/http"

	"github.com/Rabbit937/gin-demo/middleware"
	"github.com/Rabbit937/gin-demo/models"
	"github.com/Rabbit937/gin-demo/services"
	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	userService *services.UserService
}

// NewUserController 创建新的用户控制器
func NewUserController() *UserController {
	return &UserController{
		userService: services.NewUserService(),
	}
}

// Register 用户注册
func (uc *UserController) Register(c *gin.Context) {
	var req models.RegisterRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 创建用户
	user, err := uc.userService.CreateUser(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "注册失败",
			Error:   err.Error(),
		})
		return
	}

	// 生成JWT token
	token, err := middleware.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "生成token失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusCreated, models.Response{
		Code:    http.StatusCreated,
		Message: "注册成功",
		Data: models.LoginResponse{
			Token: token,
			User:  *user,
		},
	})
}

// Login 用户登录
func (uc *UserController) Login(c *gin.Context) {
	var req models.LoginRequest
	
	// 绑定请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 用户认证
	user, err := uc.userService.AuthenticateUser(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "登录失败",
			Error:   err.Error(),
		})
		return
	}

	// 生成JWT token
	token, err := middleware.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "生成token失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, models.Response{
		Code:    http.StatusOK,
		Message: "登录成功",
		Data: models.LoginResponse{
			Token: token,
			User:  *user,
		},
	})
}

// GetProfile 获取用户信息（需要认证）
func (uc *UserController) GetProfile(c *gin.Context) {
	// 从上下文获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Code:    http.StatusUnauthorized,
			Message: "未找到用户信息",
		})
		return
	}

	username, _ := c.Get("username")

	// 返回用户信息
	c.JSON(http.StatusOK, models.Response{
		Code:    http.StatusOK,
		Message: "获取用户信息成功",
		Data: gin.H{
			"user_id":  userID,
			"username": username,
		},
	})
}

// Logout 用户登出（可选实现）
func (uc *UserController) Logout(c *gin.Context) {
	// 在实际应用中，可以将token加入黑名单
	// 这里简单返回成功响应
	c.JSON(http.StatusOK, models.Response{
		Code:    http.StatusOK,
		Message: "登出成功",
	})
}
