#!/bin/bash

# 用户登录注册API测试脚本
BASE_URL="http://localhost:8080/api/v1"

echo "=== 用户登录注册API测试 ==="
echo

# 1. 测试服务器状态
echo "1. 测试服务器状态..."
curl -X GET "$BASE_URL/ping" \
  -H "Content-Type: application/json" | jq .
echo -e "\n"

# 2. 用户注册
echo "2. 用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }')

echo "$REGISTER_RESPONSE" | jq .

# 提取token
TOKEN=$(echo "$REGISTER_RESPONSE" | jq -r '.data.token')
echo "注册获得的Token: $TOKEN"
echo -e "\n"

# 3. 用户登录
echo "3. 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }')

echo "$LOGIN_RESPONSE" | jq .

# 更新token
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')
echo "登录获得的Token: $TOKEN"
echo -e "\n"

# 4. 获取用户信息（需要认证）
echo "4. 获取用户信息（需要认证）..."
curl -X GET "$BASE_URL/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

# 5. 用户登出
echo "5. 用户登出..."
curl -X POST "$BASE_URL/logout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

# 6. 测试无效token访问
echo "6. 测试无效token访问..."
curl -X GET "$BASE_URL/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid_token" | jq .
echo -e "\n"

# 7. 测试重复注册
echo "7. 测试重复注册..."
curl -X POST "$BASE_URL/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }' | jq .
echo -e "\n"

echo "=== 测试完成 ==="
